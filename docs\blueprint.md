# **App Name**: QUẢN LÝ THIẾT BỊ Y TẾ

## Core Features:

- Login: Simple login page that accepts plain text username and password (stored locally).
- QR Code Scanner: Display QR code scanner to get equipment ID.
- Dashboard: Dashboard with basic overview of equipment status (number of devices, upcoming maintenance).
- Equipment Catalog: Equipment catalog displaying a list of all medical devices with their key information (name, model, location).
- Repair Requests: Ability to submit a repair request with description.
- Maintenance Scheduling: Generate a simple maintenance schedule for medical devices.

## Style Guidelines:

- Primary color: #438797 for a professional and reliable feel.
- Background color: #d1eaf2 to maintain a light color scheme, for readability.
- Accent color: #58a7b3 to highlight important actions and status changes.
- Body and headline font: 'Inter' (sans-serif) for a modern, clean user interface.
- Use Material Design icons for a consistent and recognizable UI.
- Use a clean, grid-based layout inspired by Shadcn, maximizing screen real estate on different devices.
- Use subtle transitions and animations (fade-in, slide-in) when navigating between pages and displaying information.