@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme với màu chủ đạo vàng gold đồng điệu với logo */
    --background: 45 25% 96%;
    --foreground: 32 15% 12%;
    --card: 45 30% 98%;
    --card-foreground: 32 15% 12%;
    --popover: 45 30% 98%;
    --popover-foreground: 32 15% 12%;
    
    /* Primary color - Vàng gold chính từ logo */
    --primary: 45 75% 55%;
    --primary-foreground: 45 15% 8%;
    --primary-50: 45 80% 95%;
    --primary-100: 45 75% 88%;
    --primary-500: 45 75% 55%;
    --primary-600: 45 70% 48%;
    --primary-700: 45 65% 40%;
    
    /* Secondary color - Vàng nhạt bổ trợ */
    --secondary: 50 45% 90%;
    --secondary-foreground: 45 70% 25%;
    --secondary-50: 50 50% 96%;
    --secondary-600: 50 40% 75%;
    --secondary-700: 50 35% 65%;
    
    /* Neutral colors với warm undertone */
    --muted: 40 20% 92%;
    --muted-foreground: 32 12% 50%;
    
    /* Accent - <PERSON>àu bổ trợ hài hòa với gold */
    --accent: 35 60% 70%;
    --accent-foreground: 35 80% 15%;
    
    /* Destructive giữ nguyên màu đỏ */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    
    /* Borders và inputs với warm undertone */
    --border: 40 25% 88%;
    --input: 40 25% 90%;
    --ring: 45 75% 55%;
    
    /* Chart colors - Palette hài hòa với theme gold */
    --chart-1: 45 75% 55%;  /* Gold chính */
    --chart-2: 25 70% 50%;  /* Nâu vàng */
    --chart-3: 35 60% 45%;  /* Cam nhạt */
    --chart-4: 15 85% 60%;  /* Cam đậm */
    --chart-5: 60 50% 65%;  /* Vàng xanh */
    --color-planned: hsl(var(--chart-2));
    --color-actual: hsl(var(--chart-1));
    
    --radius: 0.5rem;
    
    /* Sidebar với tone vàng nhẹ */
    --sidebar-background: 45 35% 97%;
    --sidebar-foreground: 32 15% 12%;
    --sidebar-primary: 45 75% 55%;
    --sidebar-primary-foreground: 45 15% 8%;
    --sidebar-accent: 45 25% 94%;
    --sidebar-accent-foreground: 45 75% 45%;
    --sidebar-border: 40 25% 88%;
    --sidebar-ring: 45 75% 55%;
  }
  .dark {
    /* Dark theme với màu gold làm highlight */
    --background: 30 15% 8%;
    --foreground: 45 20% 95%;
    --card: 30 18% 10%;
    --card-foreground: 45 20% 95%;
    --popover: 30 18% 10%;
    --popover-foreground: 45 20% 95%;
    
    /* Primary color - Vàng gold sáng cho dark theme */
    --primary: 45 80% 65%;
    --primary-foreground: 30 20% 8%;
    --primary-50: 30 25% 15%;
    --primary-100: 30 30% 20%;
    --primary-500: 45 75% 60%;
    --primary-600: 45 80% 65%;
    --primary-700: 45 85% 70%;
    
    /* Secondary color - Vàng ấm cho dark */
    --secondary: 40 25% 18%;
    --secondary-foreground: 45 70% 85%;
    --secondary-50: 40 20% 12%;
    --secondary-600: 40 30% 25%;
    --secondary-700: 40 35% 35%;
    
    /* Neutral colors với warm undertone */
    --muted: 30 12% 18%;
    --muted-foreground: 40 8% 65%;
    
    /* Accent - Màu bổ trợ cho dark theme */
    --accent: 35 45% 45%;
    --accent-foreground: 35 20% 95%;
    
    /* Destructive giữ màu đỏ tối */
    --destructive: 0 65% 45%;
    --destructive-foreground: 0 0% 98%;
    
    /* Borders và inputs với warm tone */
    --border: 30 15% 22%;
    --input: 30 15% 20%;
    --ring: 45 80% 65%;
    
    /* Chart colors cho dark theme - harmonious với gold */
    --chart-1: 45 80% 65%;  /* Gold chính */
    --chart-2: 25 60% 55%;  /* Nâu ấm */
    --chart-3: 35 70% 60%;  /* Cam gold */
    --chart-4: 15 75% 65%;  /* Cam sáng */
    --chart-5: 60 45% 60%;  /* Vàng xanh */
    --color-planned: hsl(var(--chart-2));
    --color-actual: hsl(var(--chart-1));
    
    /* Sidebar với tone tối ấm */
    --sidebar-background: 30 20% 6%;
    --sidebar-foreground: 45 20% 95%;
    --sidebar-primary: 45 80% 65%;
    --sidebar-primary-foreground: 30 20% 8%;
    --sidebar-accent: 30 15% 12%;
    --sidebar-accent-foreground: 45 20% 95%;
    --sidebar-border: 30 15% 18%;
    --sidebar-ring: 45 80% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Touch Target Optimization - Mobile UX Standards */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-sm {
    @apply min-h-[40px] min-w-[40px];
  }

  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Mobile-optimized button spacing */
  .mobile-button-spacing {
    @apply gap-3 md:gap-2;
  }

  /* Enhanced touch areas for small elements */
  .touch-area-enhanced {
    @apply relative;
  }

  .touch-area-enhanced::before {
    content: '';
    @apply absolute inset-0 min-h-[44px] min-w-[44px];
    /* Center the touch area */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /* Mobile-friendly padding for interactive elements */
  .mobile-interactive {
    @apply px-4 py-3 md:px-3 md:py-2;
  }

  /* Improved spacing for mobile cards */
  .mobile-card-spacing {
    @apply p-3 gap-2 md:p-4 md:gap-3;
  }

  /* Mobile footer navigation specific styles */
  .mobile-footer-nav-item {
    @apply flex flex-col items-center justify-center gap-1 text-xs font-medium transition-all duration-200 touch-target;
    @apply hover:bg-muted/50 rounded-lg mx-1 py-2;
  }

  .mobile-footer-nav-item.active {
    @apply text-primary bg-primary/10;
  }

  .mobile-footer-nav-item.inactive {
    @apply text-muted-foreground hover:text-primary;
  }

  /* Enhanced mobile navigation transitions */
  .mobile-nav-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Mobile-specific z-index management */
  .mobile-footer-z {
    z-index: 50;
  }

  .mobile-header-z {
    z-index: 40;
  }

  /* Mobile KPI Cards optimization */
  .mobile-kpi-card {
    @apply min-h-[120px] md:min-h-[140px];
  }

  /* Mobile quick actions optimization */
  .mobile-quick-action {
    @apply h-auto flex-col gap-2 p-3 md:p-6;
  }

  .mobile-quick-action-icon {
    @apply h-4 w-4 md:h-6 md:w-6;
  }

  .mobile-quick-action-text {
    @apply text-center;
  }

  .mobile-quick-action-title {
    @apply font-medium text-xs md:text-sm;
  }

  .mobile-quick-action-desc {
    @apply text-xs text-muted-foreground hidden md:block;
  }

  /* Mobile repair request cards optimization */
  .mobile-repair-card {
    @apply p-3 md:p-4 gap-2 md:gap-3;
  }

  .mobile-repair-card-header {
    @apply p-3 pb-2 md:p-6 md:pb-4;
  }

  .mobile-repair-card-content {
    @apply p-3 pt-0 md:p-6 md:pt-0 space-y-2 md:space-y-3;
  }

  .mobile-repair-card-title {
    @apply text-sm md:text-base font-medium leading-tight;
  }

  .mobile-repair-card-description {
    @apply text-xs md:text-sm text-muted-foreground;
  }

  .mobile-repair-card-field {
    @apply flex justify-between items-start gap-2;
  }

  .mobile-repair-card-label {
    @apply text-xs md:text-sm text-muted-foreground flex-shrink-0;
  }

  .mobile-repair-card-value {
    @apply text-xs md:text-sm font-medium text-right;
  }

  /* Text truncation utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive Typography Scaling */
  .text-responsive-xs {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-base {
    font-size: clamp(1rem, 3vw, 1.125rem);
    line-height: clamp(1.5, 1.6, 1.7);
  }

  .text-responsive-lg {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  .text-responsive-xl {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    line-height: clamp(1.3, 1.4, 1.5);
  }

  .text-responsive-2xl {
    font-size: clamp(1.5rem, 5vw, 2rem);
    line-height: clamp(1.2, 1.3, 1.4);
  }

  .text-responsive-3xl {
    font-size: clamp(1.875rem, 6vw, 2.5rem);
    line-height: clamp(1.1, 1.2, 1.3);
  }

  /* Responsive headings with proper hierarchy */
  .heading-responsive-h1 {
    font-size: clamp(1.875rem, 6vw, 2.5rem);
    line-height: clamp(1.1, 1.2, 1.3);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .heading-responsive-h2 {
    font-size: clamp(1.5rem, 5vw, 2rem);
    line-height: clamp(1.2, 1.3, 1.4);
    font-weight: 600;
    letter-spacing: -0.025em;
  }

  .heading-responsive-h3 {
    font-size: clamp(1.25rem, 4vw, 1.5rem);
    line-height: clamp(1.3, 1.4, 1.5);
    font-weight: 600;
  }

  .heading-responsive-h4 {
    font-size: clamp(1.125rem, 3.5vw, 1.25rem);
    line-height: clamp(1.4, 1.5, 1.6);
    font-weight: 500;
  }

  /* Body text variants */
  .body-responsive {
    font-size: clamp(1rem, 3vw, 1.125rem);
    line-height: clamp(1.5, 1.6, 1.7);
  }

  .body-responsive-sm {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: clamp(1.4, 1.5, 1.6);
  }

  /* Caption and helper text */
  .caption-responsive {
    font-size: clamp(0.75rem, 2vw, 0.875rem);
    line-height: clamp(1.4, 1.5, 1.6);
    color: hsl(var(--muted-foreground));
  }

  /* Button text optimization */
  .button-text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    font-weight: 500;
    letter-spacing: 0.025em;
  }

  /* Progressive disclosure animations */
  .collapsible-enter {
    animation: slideDown 0.3s ease-out;
  }

  .collapsible-exit {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 1000px;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateY(0);
      max-height: 1000px;
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
  }

  /* Smooth toggle button transitions */
  .toggle-button {
    transition: all 0.2s ease-in-out;
  }

  .toggle-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Page transition animations - Performance optimized */
  .page-transition-enter {
    opacity: 0;
    transform: translateX(20px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 300ms ease-out, transform 300ms ease-out;
  }

  .page-transition-exit {
    opacity: 1;
    transform: translateX(0);
  }

  .page-transition-exit-active {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 200ms ease-in, transform 200ms ease-in;
  }

  /* Route-specific transitions */
  .route-slide-left-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .route-slide-left-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 250ms ease-out, transform 250ms ease-out;
  }

  .route-slide-right-enter {
    opacity: 0;
    transform: translateX(-100%);
  }

  .route-slide-right-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 250ms ease-out, transform 250ms ease-out;
  }

  .route-slide-up-enter {
    opacity: 0;
    transform: translateY(30px);
  }

  .route-slide-up-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease-out, transform 300ms ease-out;
  }

  /* Modal and dialog transitions */
  .modal-backdrop-enter {
    opacity: 0;
  }

  .modal-backdrop-enter-active {
    opacity: 1;
    transition: opacity 200ms ease-out;
  }

  .modal-content-enter {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }

  .modal-content-enter-active {
    opacity: 1;
    transform: scale(1) translateY(0);
    transition: opacity 200ms ease-out, transform 200ms ease-out;
  }

  /* Loading state transitions */
  .loading-fade-enter {
    opacity: 0;
  }

  .loading-fade-enter-active {
    opacity: 1;
    transition: opacity 150ms ease-out;
  }

  /* Performance-optimized utility classes */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }


}
