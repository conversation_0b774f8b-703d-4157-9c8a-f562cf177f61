<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> nhậ<PERSON> - <PERSON><PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <!-- 
    Plan Narrative & Structure:
    This page integrates an application overview with the login form.
    1. Layout: A two-column layout for larger screens. The left column serves as an infographic/welcome panel, and the right column contains the login form. On mobile, it stacks vertically.
    2. Left Column (Overview):
        - A welcoming title.
        - An expanded 3x2 grid of key features with descriptions to communicate the app's capabilities comprehensively.
        - A single, representative chart (Donut chart for equipment status) to showcase the data visualization aspect.
    3. Right Column (Login Form):
        - Replicates the UI from the user-provided screenshot.
        - A card-based form with a distinct header.
        - Input fields for credentials.
        - A primary login button.
    
    Selected Visualizations:
    - Module Icons: Goal: Organize. Method: Unicode characters in styled HTML cards. Justification: Provides a quick, scannable overview of features. (No SVG)
    - Equipment Status Chart: Goal: Compare composition. Chosen: Donut Chart (Chart.js). Justification: Ideal for showing parts of a whole in a compact space. (No SVG)
    
    Color Palette Selection:
    - Overview side uses the "Brilliant Blues" palette for consistency with the main infographic.
    - Login form side uses colors sampled from the user's screenshot (a muted teal/cyan and white) to match the requested design.
    - Background is a neutral, light blue-gray to harmonize both sides.
    
    Final check confirms that NEITHER Mermaid JS NOR SVG were used anywhere in the output.
    -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
            height: 280px;
            max-height: 320px;
        }
        .input-with-icon {
            position: relative;
        }
        .input-with-icon span {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            padding-left: 1rem;
            color: #9ca3af;
        }
        .input-with-icon input {
            padding-left: 3rem;
        }
        .feature-card {
            background-color: #EFF6FF; /* bg-blue-50 */
            padding: 1rem;
            border-radius: 0.5rem;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    </style>
</head>
<body class="bg-sky-50">

    <div class="flex flex-col md:flex-row min-h-screen">
        
        <!-- Left Column: Infographic/Overview -->
        <div class="w-full md:w-1/2 lg:w-3/5 bg-white p-8 lg:p-12 flex flex-col justify-center">
            <div class="max-w-2xl mx-auto">
                <h1 class="text-3xl md:text-4xl font-black text-[#004AAD]">Hệ Thống Quản Lý Thiết Bị Y Tế Toàn Diện</h1>
                <p class="mt-4 text-gray-600">Nền tảng thông minh giúp tối ưu hóa hiệu suất, đảm bảo an toàn và kéo dài tuổi thọ cho các tài sản y tế quan trọng.</p>

                <div class="grid grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
                    <div class="feature-card">
                        <div class="text-2xl">📊</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Tổng quan Dashboard</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Số liệu, biểu đồ và các cảnh báo quan trọng được tổng hợp tại một nơi.</p>
                    </div>
                     <div class="feature-card">
                        <div class="text-2xl">🔬</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Quản lý Thiết bị</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Danh mục chi tiết, tìm kiếm và lọc thông minh, quản lý toàn bộ vòng đời thiết bị.</p>
                    </div>
                     <div class="feature-card">
                        <div class="text-2xl">🔧</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Quản lý Sửa chữa</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Tạo, theo dõi và quản lý các yêu cầu sửa chữa một cách hiệu quả.</p>
                    </div>
                     <div class="feature-card">
                        <div class="text-2xl">📅</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Kế hoạch Bảo trì</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Chủ động lập lịch và giám sát công việc bảo trì, hiệu chuẩn, kiểm định.</p>
                    </div>
                     <div class="feature-card">
                        <div class="text-2xl">📈</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Báo cáo Trực quan</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Cung cấp biểu đồ và số liệu chi tiết giúp ra quyết định nhanh chóng.</p>
                    </div>
                     <div class="feature-card">
                        <div class="text-2xl">📱</div>
                        <h3 class="font-bold text-sm text-[#004AAD] mt-2">Quét mã QR</h3>
                        <p class="text-xs text-gray-500 mt-1 flex-grow">Truy xuất tức thì thông tin và lịch sử thiết bị chỉ với một lần quét.</p>
                    </div>
                </div>

                <div class="mt-8">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Login Form -->
        <div class="w-full md:w-1/2 lg:w-2/5 flex items-center justify-center p-8">
            <div class="w-full max-w-md">
                <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                    <div class="bg-[#4a7c82] p-8 text-center">
                        <img src="https://raw.githubusercontent.com/user-attachments/assets/56678ed9-9093-4b6e-8913-2072f4476a16" alt="Logo" class="h-16 w-16 mx-auto mb-4" />
                        <h2 class="text-2xl font-bold text-white">QUẢN LÝ THIẾT BỊ Y TẾ</h2>
                        <p class="text-white/80 text-sm mt-1">Đăng nhập vào hệ thống</p>
                    </div>
                    <div class="p-8">
                        <form action="#" method="POST">
                            <div class="mb-5">
                                <label for="username" class="block mb-2 text-sm font-medium text-gray-600">Tên đăng nhập</label>
                                <div class="input-with-icon">
                                    <span>👤</span>
                                    <input type="text" id="username" name="username" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" placeholder="Nhập tên đăng nhập" required>
                                </div>
                            </div>
                            <div class="mb-5">
                                <label for="password" class="block mb-2 text-sm font-medium text-gray-600">Mật khẩu</label>
                                <div class="input-with-icon">
                                    <span>🔒</span>
                                    <input type="password" id="password" name="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" placeholder="Nhập mật khẩu" required>
                                </div>
                            </div>
                            <button type="submit" class="w-full bg-[#5d9a9f] hover:bg-[#4a7c82] text-white font-bold py-3 px-4 rounded-lg transition-colors duration-300">Đăng nhập</button>
                        </form>
                        <div class="text-center mt-6">
                            <a href="#" class="text-sm text-gray-500 hover:text-gray-700">English</a>
                        </div>
                        <div class="text-center mt-4">
                            <p class="text-xs text-gray-400">Phát triển bởi Nguyễn Tuấn Du</p>
                            <p class="text-xs text-gray-400">Mọi chi tiết xin liên hệ: <EMAIL></p>
                        </div>
                    </div>
                </div>
                 <div class="text-center mt-6">
                    <p class="text-sm text-gray-500">Cần hỗ trợ? <a href="#" class="font-medium text-[#0079FF] hover:underline">Liên hệ quản trị viên</a></p>
                </div>
            </div>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const FONT_COLOR = '#374151';

            const commonTooltipConfig = {
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                const item = tooltipItems[0];
                                let label = item.chart.data.labels[item.dataIndex];
                                if (Array.isArray(label)) {
                                  return label.join(' ');
                                }
                                return label;
                            }
                        }
                    }
                }
            };
            
            const statusCtx = document.getElementById('statusChart')?.getContext('2d');
            if (statusCtx) {
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Hoạt động', 'Chờ sửa chữa', 'Đang bảo trì', 'Đã thanh lý'],
                        datasets: [{
                            label: 'Tình trạng thiết bị',
                            data: [120, 15, 8, 5],
                            backgroundColor: ['#0079FF', '#FFC107', '#FD7E14', '#6C757D'],
                            borderColor: '#FFFFFF',
                            borderWidth: 4,
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: FONT_COLOR,
                                    usePointStyle: true,
                                    padding: 20
                                }
                            },
                             tooltip: commonTooltipConfig.plugins.tooltip
                        },
                        cutout: '65%'
                    }
                });
            }
        });
    </script>
</body>
</html>
