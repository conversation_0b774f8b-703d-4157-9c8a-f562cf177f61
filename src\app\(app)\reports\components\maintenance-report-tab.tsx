"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, <PERSON>lt<PERSON>, Legend, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Donut } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Calendar as CalendarIcon, Wrench, CheckCircle, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { format, startOfYear, endOfYear } from "date-fns"
import { vi } from "date-fns/locale"

import { useMaintenanceReportData } from "../hooks/use-maintenance-data"
import { Skeleton } from "@/components/ui/skeleton"

interface DateRange {
  from: Date
  to: Date
}

export function MaintenanceReportTab() {
  const [dateRange, setDateRange] = React.useState<DateRange>({
    from: startOfYear(new Date()),
    to: endOfYear(new Date()),
  })

  const { data: reportData, isLoading, error } = useMaintenanceReportData(dateRange)

  const summary = reportData?.summary
  const charts = reportData?.charts

  const totalRepairValue = charts?.repairStatusDistribution.reduce((acc, cur) => acc + cur.value, 0) || 0

  return (
    <div className="space-y-4">
      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle>Bộ lọc báo cáo</CardTitle>
          <CardDescription>Chọn khoảng thời gian để xem báo cáo.</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center gap-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant={"outline"}
                className={cn(
                  "w-[300px] justify-start text-left font-normal",
                  !dateRange && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y", { locale: vi })} -{" "}
                      {format(dateRange.to, "LLL dd, y", { locale: vi })}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Chọn khoảng ngày</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={(range) => range && setDateRange({ from: range.from || new Date(), to: range.to || new Date()})}
                numberOfMonths={2}
                locale={vi}
              />
            </PopoverContent>
          </Popover>
        </CardContent>
      </Card>
      
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yêu cầu sửa chữa</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? <Skeleton className="h-8 w-16" /> : <div className="text-2xl font-bold">{summary?.totalRepairs || 0}</div>}
            <p className="text-xs text-muted-foreground">Tổng số yêu cầu trong kỳ</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tỷ lệ hoàn thành (Sửa chữa)</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            {isLoading ? <Skeleton className="h-8 w-16" /> : <div className="text-2xl font-bold">{summary?.repairCompletionRate.toFixed(1) || 0}%</div>}
            <p className="text-xs text-muted-foreground">So với tổng số yêu cầu</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Công việc bảo trì (Kế hoạch)</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? <Skeleton className="h-8 w-16" /> : <div className="text-2xl font-bold">{summary?.totalMaintenancePlanned || 0}</div>}
            <p className="text-xs text-muted-foreground">Tổng công việc trong kỳ</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tỷ lệ hoàn thành (Bảo trì)</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            {isLoading ? <Skeleton className="h-8 w-16" /> : <div className="text-2xl font-bold">{summary?.maintenanceCompletionRate.toFixed(1) || 0}%</div>}
            <p className="text-xs text-muted-foreground">So với kế hoạch</p>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Kế hoạch vs. Thực tế</CardTitle>
            <CardDescription>So sánh công việc bảo trì theo kế hoạch và thực tế hoàn thành.</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            {isLoading ? <Skeleton className="h-[350px] w-full" /> : (
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={charts?.maintenancePlanVsActual}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="planned" name="Kế hoạch" fill="var(--color-planned)" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="actual" name="Thực tế" fill="var(--color-actual)" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Tình trạng yêu cầu sửa chữa</CardTitle>
            <CardDescription>Phân bổ các yêu cầu sửa chữa theo trạng thái.</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? <Skeleton className="h-[350px] w-full" /> : (
              <ResponsiveContainer width="100%" height={350}>
                <PieChart>
                  <Pie
                    data={charts?.repairStatusDistribution}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    innerRadius={80}
                    outerRadius={120}
                    fill="#8884d8"
                    paddingAngle={5}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {charts?.repairStatusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                  <text x="50%" y="50%" textAnchor="middle" dominantBaseline="middle" className="text-2xl font-bold fill-foreground">
                    {totalRepairValue}
                  </text>
                  <text x="50%" y="50%" dy={20} textAnchor="middle" className="text-sm fill-muted-foreground">
                    Yêu cầu
                  </text>
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 