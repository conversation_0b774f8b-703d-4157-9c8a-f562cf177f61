# Cloudflare Pages Headers Configuration
# This file configures HTTP headers for the deployed site

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Cache static assets
/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images
*.png
  Cache-Control: public, max-age=31536000, immutable
*.jpg
  Cache-Control: public, max-age=31536000, immutable
*.jpeg
  Cache-Control: public, max-age=31536000, immutable
*.webp
  Cache-Control: public, max-age=31536000, immutable
*.svg
  Cache-Control: public, max-age=31536000, immutable

# Cache CSS and JS
*.css
  Cache-Control: public, max-age=31536000, immutable
*.js
  Cache-Control: public, max-age=31536000, immutable
