<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <!-- Import Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 12px;
            color: #000;
            background-color: #e5e7eb;
            line-height: 1.4;
        }
        .a4-landscape-page {
            width: 29.7cm;
            min-height: 21cm;
            padding: 1cm; /* Lề 1cm cho tất cả các cạnh */
            margin: 1cm auto;
            background: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .content-body {
            flex-grow: 1; /* <PERSON> phép kh<PERSON>i nội dung ch<PERSON>h giãn ra */
        }
        .form-input-line {
            font-family: inherit;
            font-size: inherit;
            border: none;
            border-bottom: 1px dotted #000;
            background-color: transparent;
            padding: 1px;
            outline: none;
            text-align: center;
        }
        h1, h2, .font-bold {
            font-weight: 700;
        }
        .title-main { font-size: 18px; }
        .title-sub { font-size: 16px; }

        /* Table styles */
        .data-table, .data-table th, .data-table td {
            border: 1px solid #000;
            border-collapse: collapse;
        }
        .data-table th, .data-table td {
            padding: 4px;
            text-align: center;
            vertical-align: middle;
        }
        .data-table tbody tr {
             height: 35px; /* Chiều cao cho các dòng dữ liệu */
        }
        .data-table input[type="text"] {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            background-color: transparent;
            text-align: center;
        }

        /* Signature styles */
        .signature-area {
            text-align: center;
        }
        .signature-space {
            height: 60px; /* Không gian để ký tay */
        }

        /* CSS for printing */
        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                background-color: #fff !important;
            }
            .a4-landscape-page {
                width: 100%;
                height: 100%;
                margin: 0 !important;
                padding: 1cm !important;
                box-shadow: none !important;
                border: none !important;
            }
             body > *:not(.a4-landscape-page) {
                display: none;
            }
            /* Lặp lại tiêu đề bảng trên mỗi trang */
            .data-table thead {
                display: table-header-group;
            }
            /* Ngăn các mục bị vỡ qua trang */
            .data-table tr, .signature-area {
                page-break-inside: avoid;
            }
            /* Cố định footer ở cuối mỗi trang in */
            .print-footer {
                position: fixed;
                bottom: 1cm;
                left: 1cm;
                right: 1cm;
                width: calc(100% - 2cm);
            }
             .content-body {
                padding-bottom: 30px; /* Khoảng đệm cho footer */
            }
        }
    </style>
</head>
<body>

    <div class="a4-landscape-page">
        <div class="content-body">
            <!-- Header -->
            <header>
                 <div class="flex justify-between items-start">
                    <div class="text-center w-1/4">
                        <img src="https://i.postimg.cc/W1ym4T74/cdc-logo-150.png" alt="Logo CDC" class="w-16" onerror="this.onerror=null;this.src='https://placehold.co/100x100/e2e8f0/e2e8f0?text=Logo';">
                    </div>
                    <div class="text-center w-1/2">
                         <h2 class="title-sub uppercase font-bold">TRUNG TÂM KIỂM SOÁT BỆNH TẬT THÀNH PHỐ CẦN THƠ</h2>
                         <div class="flex items-baseline justify-center font-bold">
                            <label for="department-name">KHOA/PHÒNG:</label>
                            <input type="text" id="department-name" class="form-input-line flex-grow ml-2">
                         </div>
                    </div>
                    <div class="w-1/4"></div> <!-- Spacer -->
                </div>
                 <div class="text-center mt-4">
                     <h1 class="title-main uppercase font-bold flex justify-center items-baseline">
                        KẾ HOẠCH BẢO TRÌ HIỆU CHUẨN/KIỂM ĐỊNH THIẾT BỊ NĂM
                        <input type="text" class="form-input-line w-24 ml-2">
                    </h1>
                </div>
            </header>

            <!-- Main Table -->
            <section class="mt-4">
                <table class="w-full data-table">
                    <thead class="font-bold">
                        <tr>
                            <th rowspan="2" class="w-[3%]">TT</th>
                            <th rowspan="2" class="w-[8%]">Mã TB</th>
                            <th rowspan="2" class="w-[15%]">Tên TB</th>
                            <th rowspan="2" class="w-[12%]">Khoa/Phòng sử dụng</th>
                            <th colspan="2">Đơn vị thực hiện</th>
                            <th colspan="12">Thời gian dự kiến hiệu chuẩn/kiểm định (tháng)</th>
                            <th rowspan="2" class="w-[8%]">Điểm hiệu chuẩn/kiểm định</th>
                        </tr>
                        <tr>
                            <th class="w-[5%]">Nội bộ</th>
                            <th class="w-[5%]">Thuê ngoài</th>
                            <th class="w-[2.5%]">1</th>
                            <th class="w-[2.5%]">2</th>
                            <th class="w-[2.5%]">3</th>
                            <th class="w-[2.5%]">4</th>
                            <th class="w-[2.5%]">5</th>
                            <th class="w-[2.5%]">6</th>
                            <th class="w-[2.5%]">7</th>
                            <th class="w-[2.5%]">8</th>
                            <th class="w-[2.5%]">9</th>
                            <th class="w-[2.5%]">10</th>
                            <th class="w-[2.5%]">11</th>
                            <th class="w-[2.5%]">12</th>
                        </tr>
                    </thead>
                    <tbody id="plan-table-body">
                        <!-- Các hàng dữ liệu sẽ được chèn vào đây bằng JavaScript -->
                        <!-- Ví dụ một hàng trống -->
                         <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="checkbox"></td>
                            <td><input type="text"></td>
                        </tr>
                    </tbody>
                </table>
            </section>

             <!-- Signature section -->
            <section class="mt-4">
                 <div class="flex justify-between">
                    <div class="signature-area w-1/3">
                        <p class="font-bold">Lãnh đạo Khoa/Phòng</p>
                        <div class="signature-space"></div>
                    </div>
                     <div class="w-1/3"></div> <!-- Spacer -->
                    <div class="signature-area w-1/3">
                         <p class="italic mb-2">
                            Cần Thơ, ngày <input type="text" class="form-input-line w-12">
                            tháng <input type="text" class="form-input-line w-12">
                            năm <input type="text" class="form-input-line w-20">
                        </p>
                         <p class="font-bold">Người lập</p>
                         <div class="signature-space"></div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="print-footer flex justify-between items-center text-xs">
            <span>QLTB-BM.09</span>
            <span>BH.01 (05/2024)</span>
            <span>Trang: 1/1</span>
        </footer>

    </div>

</body>
</html>
